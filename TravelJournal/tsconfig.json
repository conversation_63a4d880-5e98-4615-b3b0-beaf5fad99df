{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/constants/*": ["src/constants/*"], "@/hooks/*": ["src/hooks/*"], "@/store/*": ["src/store/*"], "@/navigation/*": ["src/navigation/*"], "@/themes/*": ["src/themes/*"], "@/database/*": ["src/database/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}