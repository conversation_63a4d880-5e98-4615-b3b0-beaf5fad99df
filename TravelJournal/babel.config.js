module.exports = function(api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'module-resolver',
        {
          root: ['./src'],
          alias: {
            '@': './src',
            '@/components': './src/components',
            '@/screens': './src/screens',
            '@/services': './src/services',
            '@/utils': './src/utils',
            '@/types': './src/types',
            '@/constants': './src/constants',
            '@/hooks': './src/hooks',
            '@/store': './src/store',
            '@/navigation': './src/navigation',
            '@/themes': './src/themes',
            '@/database': './src/database',
          },
        },
      ],
      'react-native-reanimated/plugin',
    ],
  };
};
