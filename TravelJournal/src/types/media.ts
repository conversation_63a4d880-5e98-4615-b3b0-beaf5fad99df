import { BaseEntity } from './index';
import { Location } from './location';

export interface MediaMetadata {
  width: number;
  height: number;
  fileSize: number;
  mimeType: string;
  duration?: number; // For videos
  orientation?: number;
  exif?: {
    [key: string]: any;
  };
}

export interface MediaItem extends BaseEntity {
  type: 'photo' | 'video';
  uri: string;
  thumbnail?: string;
  metadata: MediaMetadata;
  location?: Location;
  description?: string;
  tags: string[];
  journeyId?: string;
  isProcessing?: boolean;
  processingError?: string;
}

export interface MediaUploadOptions {
  quality?: number;
  allowsEditing?: boolean;
  aspect?: [number, number];
  allowsMultipleSelection?: boolean;
  mediaTypes?: 'Images' | 'Videos' | 'All';
  videoMaxDuration?: number;
}

export interface MediaProcessingOptions {
  generateThumbnail: boolean;
  extractMetadata: boolean;
  extractLocation: boolean;
  compressImage?: {
    quality: number;
    maxWidth?: number;
    maxHeight?: number;
  };
  compressVideo?: {
    quality: 'low' | 'medium' | 'high';
    maxDuration?: number;
  };
}

export interface MediaState {
  items: MediaItem[];
  selectedItems: string[];
  loading: boolean;
  uploading: boolean;
  processing: boolean;
  error: string | null;
  filters: MediaFilters;
  pagination: {
    page: number;
    limit: number;
    hasMore: boolean;
  };
}

export interface MediaFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  mediaType?: 'photo' | 'video' | 'all';
  tags?: string[];
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  journeyId?: string;
}

export interface MediaGridProps {
  items: MediaItem[];
  onItemPress: (item: MediaItem) => void;
  onItemLongPress?: (item: MediaItem) => void;
  numColumns?: number;
  showSelection?: boolean;
  selectedItems?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
}

export interface MediaItemProps {
  item: MediaItem;
  onPress: () => void;
  onLongPress?: () => void;
  isSelected?: boolean;
  showOverlay?: boolean;
  size?: 'small' | 'medium' | 'large';
}
