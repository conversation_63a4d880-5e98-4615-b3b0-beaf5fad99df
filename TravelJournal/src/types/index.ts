// Core application types
export * from './media';
export * from './location';
export * from './navigation';
export * from './theme';
export * from './api';

// Common utility types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface FilterOptions {
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius: number;
  };
  tags?: string[];
  mediaType?: 'photo' | 'video' | 'all';
  sortBy?: 'date' | 'location' | 'name';
  sortOrder?: 'asc' | 'desc';
}

export interface AppError {
  type: 'NETWORK' | 'PERMISSION' | 'STORAGE' | 'VALIDATION' | 'PROCESSING' | 'UNKNOWN';
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
}

export interface PermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: 'granted' | 'denied' | 'undetermined';
}

export interface AppPermissions {
  camera: PermissionStatus;
  mediaLibrary: PermissionStatus;
  location: PermissionStatus;
  microphone: PermissionStatus;
}
