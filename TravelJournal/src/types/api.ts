export interface APIConfig {
  googleMapsApiKey?: string;
  aiServiceApiKey?: string;
  aiServiceEndpoint?: string;
  aiServiceProvider?: 'openai' | 'google' | 'custom';
}

export interface AIServiceRequest {
  imageUri: string;
  prompt?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface AIServiceResponse {
  description: string;
  tags: string[];
  confidence: number;
  processingTime: number;
}

export interface GeocodeRequest {
  address?: string;
  latitude?: number;
  longitude?: number;
}

export interface GeocodeResponse {
  results: Array<{
    address: string;
    coordinate: {
      latitude: number;
      longitude: number;
    };
    components: {
      street?: string;
      city?: string;
      state?: string;
      country?: string;
      postalCode?: string;
    };
    types: string[];
  }>;
  status: 'OK' | 'ZERO_RESULTS' | 'OVER_QUERY_LIMIT' | 'REQUEST_DENIED' | 'INVALID_REQUEST';
}

export interface PlacesSearchRequest {
  query: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  radius?: number;
  type?: string;
}

export interface PlacesSearchResponse {
  results: Array<{
    placeId: string;
    name: string;
    address: string;
    coordinate: {
      latitude: number;
      longitude: number;
    };
    rating?: number;
    types: string[];
    photos?: Array<{
      photoReference: string;
      width: number;
      height: number;
    }>;
  }>;
  status: 'OK' | 'ZERO_RESULTS' | 'OVER_QUERY_LIMIT' | 'REQUEST_DENIED' | 'INVALID_REQUEST';
  nextPageToken?: string;
}

export interface ExportRequest {
  mediaItems: string[];
  theme: string;
  format: 'image' | 'video';
  options: {
    quality: 'low' | 'medium' | 'high';
    dimensions: {
      width: number;
      height: number;
    };
    includeMap: boolean;
    includeTimeline: boolean;
    watermark?: {
      text: string;
      position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
      opacity: number;
    };
  };
}

export interface ExportResponse {
  exportId: string;
  status: 'processing' | 'completed' | 'failed';
  progress: number;
  resultUri?: string;
  error?: string;
  estimatedTime?: number;
}

export interface SyncRequest {
  lastSyncTimestamp?: Date;
  mediaItems?: string[];
  locations?: string[];
  journeys?: string[];
}

export interface SyncResponse {
  success: boolean;
  timestamp: Date;
  changes: {
    mediaItems: {
      created: string[];
      updated: string[];
      deleted: string[];
    };
    locations: {
      created: string[];
      updated: string[];
      deleted: string[];
    };
    journeys: {
      created: string[];
      updated: string[];
      deleted: string[];
    };
  };
  conflicts?: Array<{
    type: 'media' | 'location' | 'journey';
    id: string;
    localVersion: any;
    remoteVersion: any;
  }>;
}
