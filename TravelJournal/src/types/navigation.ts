import { NavigatorScreenParams } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';
import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { MediaItem } from './media';
import { Location } from './location';

// Root Stack Navigator
export type RootStackParamList = {
  Main: NavigatorScreenParams<MainTabParamList>;
  MediaDetail: { mediaItem: MediaItem };
  EditMedia: { mediaItem: MediaItem };
  LocationPicker: { 
    onLocationSelect: (location: Location) => void;
    initialLocation?: Location;
  };
  CameraPreview: { mediaUri: string; mediaType: 'photo' | 'video' };
  ExportPreview: { exportConfig: any };
  Onboarding: undefined;
  Permissions: undefined;
};

// Main Tab Navigator
export type MainTabParamList = {
  Timeline: NavigatorScreenParams<TimelineStackParamList>;
  Camera: NavigatorScreenParams<CameraStackParamList>;
  Map: NavigatorScreenParams<MapStackParamList>;
  Settings: NavigatorScreenParams<SettingsStackParamList>;
};

// Timeline Stack
export type TimelineStackParamList = {
  TimelineScreen: undefined;
  MediaDetail: { mediaItem: MediaItem };
  EditMedia: { mediaItem: MediaItem };
};

// Camera Stack
export type CameraStackParamList = {
  CameraScreen: undefined;
  MediaPreview: { mediaUri: string; mediaType: 'photo' | 'video' };
  GalleryPicker: undefined;
};

// Map Stack
export type MapStackParamList = {
  MapScreen: undefined;
  LocationDetail: { location: Location };
  RouteView: { mediaItems: MediaItem[] };
};

// Settings Stack
export type SettingsStackParamList = {
  SettingsScreen: undefined;
  ThemeSettings: undefined;
  ExportSettings: undefined;
  APIConfiguration: undefined;
  About: undefined;
};

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> = 
  StackScreenProps<RootStackParamList, T>;

export type MainTabScreenProps<T extends keyof MainTabParamList> = 
  BottomTabScreenProps<MainTabParamList, T>;

export type TimelineStackScreenProps<T extends keyof TimelineStackParamList> = 
  StackScreenProps<TimelineStackParamList, T>;

export type CameraStackScreenProps<T extends keyof CameraStackParamList> = 
  StackScreenProps<CameraStackParamList, T>;

export type MapStackScreenProps<T extends keyof MapStackParamList> = 
  StackScreenProps<MapStackParamList, T>;

export type SettingsStackScreenProps<T extends keyof SettingsStackParamList> = 
  StackScreenProps<SettingsStackParamList, T>;

// Navigation State
export interface NavigationState {
  activeRoute: string;
  previousRoute?: string;
  params?: Record<string, any>;
  canGoBack: boolean;
}

// Modal Types
export type ModalType = 
  | 'mediaUpload'
  | 'locationPicker'
  | 'exportPreview'
  | 'settings'
  | 'confirmation'
  | 'error';

export interface ModalState {
  type: ModalType | null;
  visible: boolean;
  props?: Record<string, any>;
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
