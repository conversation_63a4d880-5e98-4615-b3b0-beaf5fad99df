import { BaseEntity } from './index';

export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Location extends BaseEntity {
  latitude: number;
  longitude: number;
  address?: string;
  placeName?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
}

export interface LocationState {
  currentLocation: Location | null;
  savedLocations: Location[];
  searchResults: Location[];
  loading: boolean;
  error: string | null;
  mapRegion: MapRegion;
  permissions: {
    granted: boolean;
    canAskAgain: boolean;
  };
}

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

export interface MapMarker {
  id: string;
  coordinate: Coordinates;
  title?: string;
  description?: string;
  mediaItems?: string[];
  clustered?: boolean;
  clusterCount?: number;
}

export interface RoutePoint {
  coordinate: Coordinates;
  timestamp: Date;
  mediaItems?: string[];
}

export interface LocationSearchResult {
  placeId: string;
  name: string;
  address: string;
  coordinate: Coordinates;
  types: string[];
}

export interface LocationServiceOptions {
  accuracy?: 'lowest' | 'low' | 'balanced' | 'high' | 'highest';
  timeout?: number;
  maximumAge?: number;
  distanceInterval?: number;
  timeInterval?: number;
}

export interface GeocodingResult {
  coordinate: Coordinates;
  address: string;
  components: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

export interface PlaceDetails {
  placeId: string;
  name: string;
  address: string;
  coordinate: Coordinates;
  phoneNumber?: string;
  website?: string;
  rating?: number;
  photos?: string[];
  types: string[];
  openingHours?: {
    isOpen: boolean;
    periods: Array<{
      open: { day: number; time: string };
      close: { day: number; time: string };
    }>;
  };
}
