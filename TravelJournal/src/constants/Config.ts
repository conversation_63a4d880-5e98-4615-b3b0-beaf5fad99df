import { Dimensions } from 'react-native';

// Screen Dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export const DIMENSIONS = {
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
  IS_SMALL_DEVICE: SCREEN_WIDTH < 375,
  IS_LARGE_DEVICE: SCREEN_WIDTH > 414,
  HEADER_HEIGHT: 56,
  TAB_BAR_HEIGHT: 60,
  STATUS_BAR_HEIGHT: 24,
};

// App Configuration
export const APP_CONFIG = {
  NAME: 'Travel Journal',
  VERSION: '1.0.0',
  BUILD_NUMBER: 1,
  BUNDLE_ID: 'com.traveljournal.app',
  DEEP_LINK_SCHEME: 'traveljournal',
  SUPPORT_EMAIL: '<EMAIL>',
  PRIVACY_POLICY_URL: 'https://traveljournal.app/privacy',
  TERMS_OF_SERVICE_URL: 'https://traveljournal.app/terms',
};

// Feature Flags
export const FEATURES = {
  AI_DESCRIPTIONS: true,
  SOCIAL_SHARING: false,
  CLOUD_SYNC: false,
  PREMIUM_THEMES: false,
  ANALYTICS: true,
  CRASH_REPORTING: true,
  PERFORMANCE_MONITORING: true,
  OFFLINE_MODE: true,
  EXPORT_VIDEO: true,
  EXPORT_PDF: false,
  MULTI_LANGUAGE: false,
};

// Media Configuration
export const MEDIA_CONFIG = {
  MAX_IMAGE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_VIDEO_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_VIDEO_DURATION: 300, // 5 minutes in seconds
  THUMBNAIL_SIZE: 200,
  PREVIEW_SIZE: 800,
  EXPORT_SIZE: 1920,
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'heic', 'webp'],
  SUPPORTED_VIDEO_FORMATS: ['mp4', 'mov', 'avi', 'mkv'],
  IMAGE_QUALITY: {
    THUMBNAIL: 0.7,
    PREVIEW: 0.8,
    EXPORT: 0.9,
    ORIGINAL: 1.0,
  },
  VIDEO_QUALITY: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
  },
};

// Location Configuration
export const LOCATION_CONFIG = {
  DEFAULT_REGION: {
    latitude: 37.7749,
    longitude: -122.4194,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  },
  ACCURACY: {
    LOWEST: 'lowest',
    LOW: 'low',
    BALANCED: 'balanced',
    HIGH: 'high',
    HIGHEST: 'highest',
  },
  TIMEOUT: 15000, // 15 seconds
  MAXIMUM_AGE: 300000, // 5 minutes
  DISTANCE_FILTER: 10, // 10 meters
  SEARCH_RADIUS: 50000, // 50km
};

// Database Configuration
export const DATABASE_CONFIG = {
  NAME: 'TravelJournal.db',
  VERSION: 1,
  DESCRIPTION: 'Travel Journal SQLite Database',
  SIZE: 5 * 1024 * 1024, // 5MB
  BACKUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  CLEANUP_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 days
};

// Cache Configuration
export const CACHE_CONFIG = {
  MAX_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_AGE: 7 * 24 * 60 * 60 * 1000, // 7 days
  CLEANUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  THUMBNAIL_CACHE_SIZE: 50 * 1024 * 1024, // 50MB
  MAP_CACHE_SIZE: 25 * 1024 * 1024, // 25MB
  METADATA_CACHE_SIZE: 5 * 1024 * 1024, // 5MB
};

// Export Configuration
export const EXPORT_CONFIG = {
  MAX_ITEMS_PER_EXPORT: 500,
  SUPPORTED_FORMATS: ['jpg', 'png', 'pdf', 'mp4'],
  DEFAULT_DIMENSIONS: {
    INSTAGRAM_STORY: { width: 1080, height: 1920 },
    INSTAGRAM_POST: { width: 1080, height: 1080 },
    FACEBOOK_POST: { width: 1200, height: 630 },
    TWITTER_POST: { width: 1024, height: 512 },
    CUSTOM: { width: 1920, height: 1080 },
  },
  VIDEO_SETTINGS: {
    FRAME_RATE: 30,
    BITRATE: 5000000, // 5 Mbps
    DURATION_PER_PHOTO: 3, // seconds
    TRANSITION_DURATION: 0.5, // seconds
  },
};

// API Configuration
export const API_CONFIG = {
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  RATE_LIMIT: {
    REQUESTS_PER_MINUTE: 60,
    REQUESTS_PER_HOUR: 1000,
  },
  ENDPOINTS: {
    GOOGLE_MAPS: 'https://maps.googleapis.com/maps/api',
    GOOGLE_PLACES: 'https://maps.googleapis.com/maps/api/place',
    OPENAI: 'https://api.openai.com/v1',
  },
};

// Performance Configuration
export const PERFORMANCE_CONFIG = {
  LAZY_LOADING_THRESHOLD: 10,
  VIRTUAL_LIST_ITEM_HEIGHT: 120,
  IMAGE_CACHE_SIZE: 20,
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,
  ANIMATION_DURATION: 250,
  LONG_PRESS_DURATION: 500,
};

// Security Configuration
export const SECURITY_CONFIG = {
  ENCRYPTION_KEY_SIZE: 256,
  HASH_ROUNDS: 12,
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  SECURE_STORAGE_KEYS: {
    API_KEYS: 'api_keys',
    USER_PREFERENCES: 'user_preferences',
    ENCRYPTION_KEY: 'encryption_key',
  },
};

// Development Configuration
export const DEV_CONFIG = {
  ENABLE_FLIPPER: __DEV__,
  ENABLE_REACTOTRON: __DEV__,
  LOG_LEVEL: __DEV__ ? 'debug' : 'error',
  MOCK_LOCATION: __DEV__,
  MOCK_CAMERA: __DEV__,
  SKIP_PERMISSIONS: __DEV__,
};
