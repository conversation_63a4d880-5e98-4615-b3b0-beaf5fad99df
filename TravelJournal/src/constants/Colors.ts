import { ColorPalette } from '@/types/theme';

// Light Theme Colors
export const LIGHT_COLORS: ColorPalette = {
  primary: '#2196F3',
  primaryDark: '#1976D2',
  primaryLight: '#BBDEFB',
  secondary: '#FF9800',
  secondaryDark: '#F57C00',
  secondaryLight: '#FFE0B2',
  accent: '#E91E63',
  background: '#FFFFFF',
  surface: '#F5F5F5',
  card: '#FFFFFF',
  text: '#212121',
  textSecondary: '#757575',
  textDisabled: '#BDBDBD',
  border: '#E0E0E0',
  divider: '#EEEEEE',
  error: '#F44336',
  warning: '#FF9800',
  success: '#4CAF50',
  info: '#2196F3',
  overlay: 'rgba(0, 0, 0, 0.5)',
  shadow: '#000000',
};

// Dark Theme Colors
export const DARK_COLORS: ColorPalette = {
  primary: '#64B5F6',
  primaryDark: '#1976D2',
  primaryLight: '#E3F2FD',
  secondary: '#FFB74D',
  secondaryDark: '#F57C00',
  secondaryLight: '#FFF3E0',
  accent: '#F48FB1',
  background: '#121212',
  surface: '#1E1E1E',
  card: '#2C2C2C',
  text: '#FFFFFF',
  textSecondary: '#B3B3B3',
  textDisabled: '#666666',
  border: '#333333',
  divider: '#2C2C2C',
  error: '#EF5350',
  warning: '#FFB74D',
  success: '#66BB6A',
  info: '#64B5F6',
  overlay: 'rgba(0, 0, 0, 0.7)',
  shadow: '#000000',
};

// Vibrant Theme Colors
export const VIBRANT_COLORS: ColorPalette = {
  primary: '#FF6B35',
  primaryDark: '#E55A2B',
  primaryLight: '#FFB3A0',
  secondary: '#F7931E',
  secondaryDark: '#E8841A',
  secondaryLight: '#FDD5A8',
  accent: '#C70039',
  background: '#FFF8F3',
  surface: '#FFFFFF',
  card: '#FFFFFF',
  text: '#2C3E50',
  textSecondary: '#7F8C8D',
  textDisabled: '#BDC3C7',
  border: '#ECF0F1',
  divider: '#F8F9FA',
  error: '#E74C3C',
  warning: '#F39C12',
  success: '#27AE60',
  info: '#3498DB',
  overlay: 'rgba(44, 62, 80, 0.5)',
  shadow: '#34495E',
};

// Export Theme Colors
export const EXPORT_THEME_COLORS = {
  classic: {
    background: '#FFFFFF',
    primary: '#2C3E50',
    secondary: '#3498DB',
    text: '#2C3E50',
    accent: '#E74C3C',
  },
  modern: {
    background: '#F8F9FA',
    primary: '#495057',
    secondary: '#6C757D',
    text: '#212529',
    accent: '#007BFF',
  },
  elegant: {
    background: '#FAFAFA',
    primary: '#424242',
    secondary: '#757575',
    text: '#212121',
    accent: '#FF5722',
  },
  vibrant: {
    background: '#FFF3E0',
    primary: '#FF6F00',
    secondary: '#FF8F00',
    text: '#E65100',
    accent: '#FF3D00',
  },
  nature: {
    background: '#F1F8E9',
    primary: '#33691E',
    secondary: '#689F38',
    text: '#1B5E20',
    accent: '#8BC34A',
  },
  ocean: {
    background: '#E0F2F1',
    primary: '#00695C',
    secondary: '#00897B',
    text: '#004D40',
    accent: '#26A69A',
  },
  sunset: {
    background: '#FFF8E1',
    primary: '#E65100',
    secondary: '#FF9800',
    text: '#BF360C',
    accent: '#FF5722',
  },
  monochrome: {
    background: '#FAFAFA',
    primary: '#212121',
    secondary: '#424242',
    text: '#212121',
    accent: '#757575',
  },
};

// Status Colors
export const STATUS_COLORS = {
  online: '#4CAF50',
  offline: '#9E9E9E',
  syncing: '#2196F3',
  error: '#F44336',
  warning: '#FF9800',
  processing: '#FF9800',
  completed: '#4CAF50',
};

// Map Colors
export const MAP_COLORS = {
  routeColor: '#2196F3',
  routeColorActive: '#FF6B35',
  markerColor: '#FF6B35',
  markerColorSelected: '#C70039',
  clusterColor: '#2196F3',
  clusterTextColor: '#FFFFFF',
  userLocationColor: '#4CAF50',
};

// Gradient Colors
export const GRADIENTS = {
  primary: ['#2196F3', '#21CBF3'],
  secondary: ['#FF9800', '#FFB74D'],
  accent: ['#E91E63', '#F48FB1'],
  sunset: ['#FF6B35', '#F7931E'],
  ocean: ['#00BCD4', '#4FC3F7'],
  forest: ['#4CAF50', '#8BC34A'],
  purple: ['#9C27B0', '#BA68C8'],
  warm: ['#FF5722', '#FF8A65'],
};
