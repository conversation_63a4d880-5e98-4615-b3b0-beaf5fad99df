import React, { createContext, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { useSelector, useDispatch } from 'react-redux';

// Types
import { ThemeConfig, ThemeContextValue, ThemeMode } from '@/types/theme';

// Themes
import { lightTheme } from './lightTheme';
import { darkTheme } from './darkTheme';

// Store
import { selectCurrentTheme, selectThemeMode } from '@/store/selectors/themeSelectors';
import { setTheme, setThemeMode } from '@/store/slices/themeSlice';

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const systemColorScheme = useColorScheme();
  const currentThemeId = useSelector(selectCurrentTheme);
  const themeMode = useSelector(selectThemeMode);
  
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig>(lightTheme);

  // Available themes
  const themes = {
    light: lightTheme,
    dark: darkTheme,
  };

  // Determine which theme to use
  useEffect(() => {
    let themeToUse: ThemeConfig;

    if (themeMode === 'system') {
      themeToUse = systemColorScheme === 'dark' ? darkTheme : lightTheme;
    } else if (themeMode === 'dark') {
      themeToUse = darkTheme;
    } else {
      themeToUse = lightTheme;
    }

    // If a specific theme is selected, use that instead
    if (currentThemeId && themes[currentThemeId as keyof typeof themes]) {
      themeToUse = themes[currentThemeId as keyof typeof themes];
    }

    setCurrentTheme(themeToUse);
  }, [themeMode, currentThemeId, systemColorScheme]);

  const toggleTheme = () => {
    const newMode: ThemeMode = currentTheme.isDark ? 'light' : 'dark';
    dispatch(setThemeMode(newMode));
  };

  const handleSetTheme = (themeId: string) => {
    dispatch(setTheme(themeId));
  };

  const handleSetThemeMode = (mode: ThemeMode) => {
    dispatch(setThemeMode(mode));
  };

  const contextValue: ThemeContextValue = {
    theme: currentTheme,
    isDark: currentTheme.isDark,
    toggleTheme,
    setTheme: handleSetTheme,
    setThemeMode: handleSetThemeMode,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeConfig => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context.theme;
};

export const useThemeContext = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};
