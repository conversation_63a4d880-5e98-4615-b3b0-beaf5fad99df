import { Middleware } from '@reduxjs/toolkit';

// Analytics events we want to track
const TRACKED_ACTIONS = [
  'media/addMediaItem',
  'media/removeMediaItem',
  'location/setCurrentLocation',
  'ui/showModal',
  'settings/updatePreferences',
];

export const analyticsMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // Only track specific actions and when analytics is enabled
  const state = store.getState();
  const analyticsEnabled = state.settings?.preferences?.analytics;

  if (analyticsEnabled && TRACKED_ACTIONS.includes(action.type)) {
    // In a real app, you would send this to your analytics service
    if (__DEV__) {
      console.log('Analytics Event:', {
        action: action.type,
        payload: action.payload,
        timestamp: new Date().toISOString(),
      });
    }

    // Example: Send to analytics service
    // AnalyticsService.track(action.type, {
    //   payload: action.payload,
    //   timestamp: new Date().toISOString(),
    // });
  }

  return result;
};
