import { Middleware } from '@reduxjs/toolkit';
import { showToast } from '../slices/uiSlice';

export const errorMiddleware: Middleware = (store) => (next) => (action) => {
  // Check if action is an error action
  if (action.type.endsWith('/rejected')) {
    const errorMessage = action.payload?.message || 'An error occurred';
    
    // Dispatch toast notification for errors
    store.dispatch(showToast({
      message: errorMessage,
      type: 'error',
      duration: 5000,
    }));

    // Log error in development
    if (__DEV__) {
      console.error('Redux Error:', {
        type: action.type,
        payload: action.payload,
        error: action.error,
      });
    }
  }

  return next(action);
};
