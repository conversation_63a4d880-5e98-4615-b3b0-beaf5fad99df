import { RootState } from '../index';

// UI Selectors
export const selectIsFirstLaunch = (state: RootState) => state.ui.isFirstLaunch;
export const selectHasCompletedOnboarding = (state: RootState) => state.ui.hasCompletedOnboarding;
export const selectActiveScreen = (state: RootState) => state.ui.activeScreen;
export const selectPreviousScreen = (state: RootState) => state.ui.previousScreen;
export const selectGlobalLoading = (state: RootState) => state.ui.loading.global;
export const selectScreenLoading = (screen: string) => (state: RootState) => 
  state.ui.loading.screens[screen] || false;

// Modal Selectors
export const selectModalState = (state: RootState) => state.ui.modal;
export const selectIsModalVisible = (state: RootState) => state.ui.modal.visible;
export const selectModalType = (state: RootState) => state.ui.modal.type;
export const selectModalProps = (state: RootState) => state.ui.modal.props;

// Toast Selectors
export const selectToastState = (state: RootState) => state.ui.toast;
export const selectIsToastVisible = (state: RootState) => state.ui.toast.visible;

// Bottom Sheet Selectors
export const selectBottomSheetState = (state: RootState) => state.ui.bottomSheet;
export const selectIsBottomSheetVisible = (state: RootState) => state.ui.bottomSheet.visible;

// Permission Selectors
export const selectPermissions = (state: RootState) => state.ui.permissions;
export const selectCameraPermission = (state: RootState) => state.ui.permissions.camera;
export const selectLocationPermission = (state: RootState) => state.ui.permissions.location;
export const selectMediaLibraryPermission = (state: RootState) => state.ui.permissions.mediaLibrary;
export const selectMicrophonePermission = (state: RootState) => state.ui.permissions.microphone;

export const selectHasRequiredPermissions = (state: RootState) => {
  const permissions = state.ui.permissions;
  return permissions.camera && permissions.mediaLibrary && permissions.location;
};
