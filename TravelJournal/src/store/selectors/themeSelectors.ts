import { RootState } from '../index';

export const selectCurrentTheme = (state: RootState) => state.theme.currentTheme;
export const selectThemeMode = (state: RootState) => state.theme.themeMode;
export const selectIsDarkMode = (state: RootState) => state.theme.isDarkMode;
export const selectSystemTheme = (state: RootState) => state.theme.systemTheme;
export const selectAvailableThemes = (state: RootState) => state.theme.availableThemes;
export const selectExportThemes = (state: RootState) => state.theme.exportThemes;
export const selectCustomThemes = (state: RootState) => state.theme.customThemes;
