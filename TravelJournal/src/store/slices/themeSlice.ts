import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ThemeState, ThemeMode } from '@/types/theme';

const initialState: ThemeState = {
  currentTheme: 'light',
  themeMode: 'system',
  isDarkMode: false,
  systemTheme: true,
  availableThemes: [],
  exportThemes: [],
  customThemes: [],
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<string>) => {
      state.currentTheme = action.payload;
    },
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.themeMode = action.payload;
      state.systemTheme = action.payload === 'system';
    },
    setIsDarkMode: (state, action: PayloadAction<boolean>) => {
      state.isDarkMode = action.payload;
    },
    toggleTheme: (state) => {
      state.isDarkMode = !state.isDarkMode;
      state.themeMode = state.isDarkMode ? 'dark' : 'light';
      state.systemTheme = false;
    },
  },
});

export const { setTheme, setThemeMode, setIsDarkMode, toggleTheme } = themeSlice.actions;
export default themeSlice.reducer;
