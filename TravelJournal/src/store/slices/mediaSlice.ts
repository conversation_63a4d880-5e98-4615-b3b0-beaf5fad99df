import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { MediaState, MediaItem, MediaFilters } from '@/types/media';

const initialState: MediaState = {
  items: [],
  selectedItems: [],
  loading: false,
  uploading: false,
  processing: false,
  error: null,
  filters: {
    mediaType: 'all',
    tags: [],
  },
  pagination: {
    page: 1,
    limit: 20,
    hasMore: true,
  },
};

const mediaSlice = createSlice({
  name: 'media',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setUploading: (state, action: PayloadAction<boolean>) => {
      state.uploading = action.payload;
    },
    setProcessing: (state, action: PayloadAction<boolean>) => {
      state.processing = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    addMediaItem: (state, action: PayloadAction<MediaItem>) => {
      state.items.unshift(action.payload);
    },
    updateMediaItem: (state, action: PayloadAction<{ id: string; updates: Partial<MediaItem> }>) => {
      const index = state.items.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.items[index] = { ...state.items[index], ...action.payload.updates };
      }
    },
    removeMediaItem: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
    },
    setMediaItems: (state, action: PayloadAction<MediaItem[]>) => {
      state.items = action.payload;
    },
    setSelectedItems: (state, action: PayloadAction<string[]>) => {
      state.selectedItems = action.payload;
    },
    toggleItemSelection: (state, action: PayloadAction<string>) => {
      const itemId = action.payload;
      const index = state.selectedItems.indexOf(itemId);
      if (index === -1) {
        state.selectedItems.push(itemId);
      } else {
        state.selectedItems.splice(index, 1);
      }
    },
    clearSelection: (state) => {
      state.selectedItems = [];
    },
    setFilters: (state, action: PayloadAction<MediaFilters>) => {
      state.filters = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<MediaFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {
        mediaType: 'all',
        tags: [],
      };
    },
    setPagination: (state, action: PayloadAction<{ page: number; hasMore: boolean }>) => {
      state.pagination.page = action.payload.page;
      state.pagination.hasMore = action.payload.hasMore;
    },
  },
});

export const {
  setLoading,
  setUploading,
  setProcessing,
  setError,
  addMediaItem,
  updateMediaItem,
  removeMediaItem,
  setMediaItems,
  setSelectedItems,
  toggleItemSelection,
  clearSelection,
  setFilters,
  updateFilters,
  clearFilters,
  setPagination,
} = mediaSlice.actions;

export default mediaSlice.reducer;
