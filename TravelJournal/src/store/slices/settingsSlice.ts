import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { APIConfig } from '@/types/api';

interface SettingsState {
  apiConfig: APIConfig;
  preferences: {
    autoBackup: boolean;
    highQualityImages: boolean;
    locationTracking: boolean;
    aiDescriptions: boolean;
    notifications: boolean;
    analytics: boolean;
  };
  export: {
    defaultFormat: 'image' | 'video';
    defaultQuality: 'low' | 'medium' | 'high';
    includeWatermark: boolean;
    watermarkText: string;
  };
  privacy: {
    shareLocation: boolean;
    shareAnalytics: boolean;
    allowCrashReporting: boolean;
  };
  storage: {
    cacheSize: number;
    maxCacheAge: number;
    autoCleanup: boolean;
  };
}

const initialState: SettingsState = {
  apiConfig: {
    googleMapsApiKey: undefined,
    aiServiceApiKey: undefined,
    aiServiceEndpoint: undefined,
    aiServiceProvider: 'openai',
  },
  preferences: {
    autoBackup: false,
    highQualityImages: true,
    locationTracking: true,
    aiDescriptions: false,
    notifications: true,
    analytics: true,
  },
  export: {
    defaultFormat: 'image',
    defaultQuality: 'high',
    includeWatermark: false,
    watermarkText: 'Travel Journal',
  },
  privacy: {
    shareLocation: true,
    shareAnalytics: true,
    allowCrashReporting: true,
  },
  storage: {
    cacheSize: 100, // MB
    maxCacheAge: 7, // days
    autoCleanup: true,
  },
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    updateApiConfig: (state, action: PayloadAction<Partial<APIConfig>>) => {
      state.apiConfig = { ...state.apiConfig, ...action.payload };
    },
    updatePreferences: (state, action: PayloadAction<Partial<SettingsState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    updateExportSettings: (state, action: PayloadAction<Partial<SettingsState['export']>>) => {
      state.export = { ...state.export, ...action.payload };
    },
    updatePrivacySettings: (state, action: PayloadAction<Partial<SettingsState['privacy']>>) => {
      state.privacy = { ...state.privacy, ...action.payload };
    },
    updateStorageSettings: (state, action: PayloadAction<Partial<SettingsState['storage']>>) => {
      state.storage = { ...state.storage, ...action.payload };
    },
    resetSettings: (state) => {
      return { ...initialState, apiConfig: state.apiConfig }; // Keep API keys
    },
  },
});

export const {
  updateApiConfig,
  updatePreferences,
  updateExportSettings,
  updatePrivacySettings,
  updateStorageSettings,
  resetSettings,
} = settingsSlice.actions;

export default settingsSlice.reducer;
