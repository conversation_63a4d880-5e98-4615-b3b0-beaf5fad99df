import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ModalType } from '@/types/navigation';

interface UIState {
  activeScreen: string;
  previousScreen?: string;
  modal: {
    type: ModalType | null;
    visible: boolean;
    props?: Record<string, any>;
  };
  loading: {
    global: boolean;
    screens: Record<string, boolean>;
  };
  toast: {
    visible: boolean;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
    duration: number;
  };
  bottomSheet: {
    visible: boolean;
    content: string | null;
    props?: Record<string, any>;
  };
  permissions: {
    camera: boolean;
    mediaLibrary: boolean;
    location: boolean;
    microphone: boolean;
  };
  isFirstLaunch: boolean;
  hasCompletedOnboarding: boolean;
}

const initialState: UIState = {
  activeScreen: 'Timeline',
  modal: {
    type: null,
    visible: false,
  },
  loading: {
    global: false,
    screens: {},
  },
  toast: {
    visible: false,
    message: '',
    type: 'info',
    duration: 3000,
  },
  bottomSheet: {
    visible: false,
    content: null,
  },
  permissions: {
    camera: false,
    mediaLibrary: false,
    location: false,
    microphone: false,
  },
  isFirstLaunch: true,
  hasCompletedOnboarding: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setActiveScreen: (state, action: PayloadAction<string>) => {
      state.previousScreen = state.activeScreen;
      state.activeScreen = action.payload;
    },
    showModal: (state, action: PayloadAction<{ type: ModalType; props?: Record<string, any> }>) => {
      state.modal = {
        type: action.payload.type,
        visible: true,
        props: action.payload.props,
      };
    },
    hideModal: (state) => {
      state.modal = {
        type: null,
        visible: false,
        props: undefined,
      };
    },
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    setScreenLoading: (state, action: PayloadAction<{ screen: string; loading: boolean }>) => {
      state.loading.screens[action.payload.screen] = action.payload.loading;
    },
    showToast: (state, action: PayloadAction<{
      message: string;
      type?: 'success' | 'error' | 'warning' | 'info';
      duration?: number;
    }>) => {
      state.toast = {
        visible: true,
        message: action.payload.message,
        type: action.payload.type || 'info',
        duration: action.payload.duration || 3000,
      };
    },
    hideToast: (state) => {
      state.toast.visible = false;
    },
    showBottomSheet: (state, action: PayloadAction<{
      content: string;
      props?: Record<string, any>;
    }>) => {
      state.bottomSheet = {
        visible: true,
        content: action.payload.content,
        props: action.payload.props,
      };
    },
    hideBottomSheet: (state) => {
      state.bottomSheet = {
        visible: false,
        content: null,
        props: undefined,
      };
    },
    setPermission: (state, action: PayloadAction<{
      type: keyof UIState['permissions'];
      granted: boolean;
    }>) => {
      state.permissions[action.payload.type] = action.payload.granted;
    },
    setPermissions: (state, action: PayloadAction<Partial<UIState['permissions']>>) => {
      state.permissions = { ...state.permissions, ...action.payload };
    },
    setIsFirstLaunch: (state, action: PayloadAction<boolean>) => {
      state.isFirstLaunch = action.payload;
    },
    setHasCompletedOnboarding: (state, action: PayloadAction<boolean>) => {
      state.hasCompletedOnboarding = action.payload;
    },
  },
});

export const {
  setActiveScreen,
  showModal,
  hideModal,
  setGlobalLoading,
  setScreenLoading,
  showToast,
  hideToast,
  showBottomSheet,
  hideBottomSheet,
  setPermission,
  setPermissions,
  setIsFirstLaunch,
  setHasCompletedOnboarding,
} = uiSlice.actions;

export default uiSlice.reducer;
