import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LocationState, Location, MapRegion } from '@/types/location';
import { LOCATION_CONFIG } from '@/constants/Config';

const initialState: LocationState = {
  currentLocation: null,
  savedLocations: [],
  searchResults: [],
  loading: false,
  error: null,
  mapRegion: LOCATION_CONFIG.DEFAULT_REGION,
  permissions: {
    granted: false,
    canAskAgain: true,
  },
};

const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setCurrentLocation: (state, action: PayloadAction<Location | null>) => {
      state.currentLocation = action.payload;
    },
    addSavedLocation: (state, action: PayloadAction<Location>) => {
      const exists = state.savedLocations.find(loc => loc.id === action.payload.id);
      if (!exists) {
        state.savedLocations.push(action.payload);
      }
    },
    removeSavedLocation: (state, action: PayloadAction<string>) => {
      state.savedLocations = state.savedLocations.filter(loc => loc.id !== action.payload);
    },
    updateSavedLocation: (state, action: PayloadAction<{ id: string; updates: Partial<Location> }>) => {
      const index = state.savedLocations.findIndex(loc => loc.id === action.payload.id);
      if (index !== -1) {
        state.savedLocations[index] = { ...state.savedLocations[index], ...action.payload.updates };
      }
    },
    setSearchResults: (state, action: PayloadAction<Location[]>) => {
      state.searchResults = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    setMapRegion: (state, action: PayloadAction<MapRegion>) => {
      state.mapRegion = action.payload;
    },
    setPermissions: (state, action: PayloadAction<{ granted: boolean; canAskAgain: boolean }>) => {
      state.permissions = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setCurrentLocation,
  addSavedLocation,
  removeSavedLocation,
  updateSavedLocation,
  setSearchResults,
  clearSearchResults,
  setMapRegion,
  setPermissions,
} = locationSlice.actions;

export default locationSlice.reducer;
