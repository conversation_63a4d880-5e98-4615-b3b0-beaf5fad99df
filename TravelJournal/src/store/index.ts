import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { combineReducers } from '@reduxjs/toolkit';

// Slices
import mediaSlice from './slices/mediaSlice';
import locationSlice from './slices/locationSlice';
import uiSlice from './slices/uiSlice';
import settingsSlice from './slices/settingsSlice';
import themeSlice from './slices/themeSlice';

// Middleware
import { errorMiddleware } from './middleware/errorMiddleware';
import { analyticsMiddleware } from './middleware/analyticsMiddleware';

// Persist configuration
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['settings', 'theme'], // Only persist settings and theme
  blacklist: ['ui'], // Don't persist UI state
};

const rootReducer = combineReducers({
  media: mediaSlice,
  location: locationSlice,
  ui: uiSlice,
  settings: settingsSlice,
  theme: themeSlice,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/FLUSH',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PERSIST',
          'persist/PURGE',
          'persist/REGISTER',
        ],
      },
    }).concat(errorMiddleware, analyticsMiddleware),
  devTools: __DEV__,
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
