import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch } from 'react-redux';

// Types
import { RootStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

// Store
import { setIsFirstLaunch, setHasCompletedOnboarding } from '@/store/slices/uiSlice';

type Props = RootStackScreenProps<'Onboarding'>;

export const OnboardingScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();
  const dispatch = useDispatch();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.lg,
    },
    title: {
      fontSize: theme.typography.fontSize.xxxl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.primary,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.xl,
      fontFamily: theme.typography.fontFamily.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
      textAlign: 'center',
    },
    description: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.md,
      marginBottom: theme.spacing.xxl,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  const handleGetStarted = () => {
    dispatch(setIsFirstLaunch(false));
    dispatch(setHasCompletedOnboarding(true));
    navigation.replace('Permissions');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>📱</Text>
        <Text style={styles.title}>Travel Journal</Text>
        <Text style={styles.subtitle}>Capture Your Adventures</Text>
        
        <Text style={styles.description}>
          Create beautiful travel journals with photos, videos, and locations. 
          Organize your memories chronologically and export them as stunning visual stories.
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="Get Started"
            onPress={handleGetStarted}
            variant="primary"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
