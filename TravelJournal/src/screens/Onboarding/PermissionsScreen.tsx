import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDispatch } from 'react-redux';

// Types
import { RootStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

// Store
import { setPermissions } from '@/store/slices/uiSlice';

type Props = RootStackScreenProps<'Permissions'>;

export const PermissionsScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();
  const dispatch = useDispatch();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.lg,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    description: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.md,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    permissionItem: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      ...theme.shadows.small,
    },
    permissionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    permissionDescription: {
      fontSize: theme.typography.fontSize.sm,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.sm,
    },
    buttonContainer: {
      marginTop: theme.spacing.xl,
      gap: theme.spacing.md,
    },
  });

  const handleGrantPermissions = async () => {
    // TODO: Request actual permissions
    // For now, just simulate granting permissions
    dispatch(setPermissions({
      camera: true,
      mediaLibrary: true,
      location: true,
      microphone: true,
    }));
    
    navigation.replace('Main');
  };

  const handleSkip = () => {
    navigation.replace('Main');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>Permissions Required</Text>
        <Text style={styles.description}>
          Travel Journal needs access to these features to provide the best experience:
        </Text>

        <View style={styles.permissionItem}>
          <Text style={styles.permissionTitle}>📷 Camera</Text>
          <Text style={styles.permissionDescription}>
            Take photos and videos for your travel journal
          </Text>
        </View>

        <View style={styles.permissionItem}>
          <Text style={styles.permissionTitle}>🖼️ Photo Library</Text>
          <Text style={styles.permissionDescription}>
            Access existing photos and videos from your device
          </Text>
        </View>

        <View style={styles.permissionItem}>
          <Text style={styles.permissionTitle}>📍 Location</Text>
          <Text style={styles.permissionDescription}>
            Automatically tag your photos with location information
          </Text>
        </View>

        <View style={styles.permissionItem}>
          <Text style={styles.permissionTitle}>🎤 Microphone</Text>
          <Text style={styles.permissionDescription}>
            Record audio for videos in your travel journal
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Grant Permissions"
            onPress={handleGrantPermissions}
            variant="primary"
          />
          
          <Button
            title="Skip for Now"
            onPress={handleSkip}
            variant="outline"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
