import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { TimelineStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = TimelineStackScreenProps<'TimelineScreen'>;

export const TimelineScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.medium,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    description: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      lineHeight: theme.typography.lineHeight.md,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    buttonContainer: {
      marginTop: theme.spacing.xl,
      gap: theme.spacing.md,
    },
  });

  const handleMediaDetailPress = () => {
    // Mock media item for demonstration
    const mockMediaItem = {
      id: '1',
      type: 'photo' as const,
      uri: 'https://example.com/photo.jpg',
      metadata: {
        width: 1920,
        height: 1080,
        fileSize: 2048000,
        mimeType: 'image/jpeg',
      },
      tags: ['travel', 'vacation'],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    navigation.navigate('MediaDetail', { mediaItem: mockMediaItem });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>Travel Journal</Text>
        <Text style={styles.subtitle}>Timeline View</Text>
        
        <Text style={styles.description}>
          Welcome to your travel journal timeline! This is where all your memories will be organized chronologically.
        </Text>
        
        <Text style={styles.description}>
          Features coming soon:
          {'\n'}• Photo and video timeline
          {'\n'}• Location-based organization
          {'\n'}• Journey grouping
          {'\n'}• Search and filtering
          {'\n'}• Rich descriptions and tags
        </Text>

        <View style={styles.buttonContainer}>
          <Button
            title="View Sample Media"
            onPress={handleMediaDetailPress}
            variant="primary"
          />
          
          <Button
            title="Go to Camera"
            onPress={() => navigation.navigate('Camera' as any)}
            variant="secondary"
          />
          
          <Button
            title="View Map"
            onPress={() => navigation.navigate('Map' as any)}
            variant="outline"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
