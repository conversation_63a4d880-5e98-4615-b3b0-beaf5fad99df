import React from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { RootStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = RootStackScreenProps<'MediaDetail'>;

export const MediaDetailScreen: React.FC<Props> = ({ route, navigation }) => {
  const theme = useTheme();
  const { mediaItem } = route.params;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    mediaContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      ...theme.shadows.medium,
    },
    mediaPlaceholder: {
      width: '100%',
      height: 200,
      backgroundColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    placeholderText: {
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.textSecondary,
      fontFamily: theme.typography.fontFamily.medium,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.divider,
    },
    infoLabel: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.medium,
      color: theme.colors.textSecondary,
    },
    infoValue: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.text,
    },
    description: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.text,
      lineHeight: theme.typography.lineHeight.md,
      marginTop: theme.spacing.lg,
    },
    buttonContainer: {
      marginTop: theme.spacing.xl,
      gap: theme.spacing.md,
    },
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleEditPress = () => {
    navigation.navigate('EditMedia', { mediaItem });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.mediaContainer}>
          <View style={styles.mediaPlaceholder}>
            <Text style={styles.placeholderText}>
              {mediaItem.type === 'photo' ? '📷' : '🎥'}
            </Text>
            <Text style={styles.placeholderText}>
              {mediaItem.type === 'photo' ? 'Photo' : 'Video'}
            </Text>
          </View>

          <Text style={styles.title}>
            {mediaItem.type === 'photo' ? 'Photo Details' : 'Video Details'}
          </Text>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Type</Text>
            <Text style={styles.infoValue}>{mediaItem.type}</Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Size</Text>
            <Text style={styles.infoValue}>
              {formatFileSize(mediaItem.metadata.fileSize)}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Dimensions</Text>
            <Text style={styles.infoValue}>
              {mediaItem.metadata.width} × {mediaItem.metadata.height}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Created</Text>
            <Text style={styles.infoValue}>
              {mediaItem.createdAt.toLocaleDateString()}
            </Text>
          </View>

          {mediaItem.tags.length > 0 && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Tags</Text>
              <Text style={styles.infoValue}>
                {mediaItem.tags.join(', ')}
              </Text>
            </View>
          )}

          {mediaItem.description && (
            <Text style={styles.description}>
              {mediaItem.description}
            </Text>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Edit Media"
            onPress={handleEditPress}
            variant="primary"
          />
          
          <Button
            title="Share"
            onPress={() => {/* TODO: Implement sharing */}}
            variant="secondary"
          />
          
          <Button
            title="Delete"
            onPress={() => {/* TODO: Implement deletion */}}
            variant="outline"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
