import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { MapStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = MapStackScreenProps<'LocationDetail'>;

export const LocationDetailScreen: React.FC<Props> = ({ route, navigation }) => {
  const theme = useTheme();
  const { location } = route.params;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    locationContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      ...theme.shadows.medium,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.divider,
    },
    infoLabel: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.medium,
      color: theme.colors.textSecondary,
    },
    infoValue: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.text,
      flex: 1,
      textAlign: 'right',
    },
    buttonContainer: {
      marginTop: theme.spacing.xl,
      gap: theme.spacing.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.locationContainer}>
          <Text style={styles.title}>
            {location.placeName || 'Unknown Location'}
          </Text>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Coordinates</Text>
            <Text style={styles.infoValue}>
              {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
            </Text>
          </View>

          {location.address && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Address</Text>
              <Text style={styles.infoValue}>{location.address}</Text>
            </View>
          )}

          {location.city && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>City</Text>
              <Text style={styles.infoValue}>{location.city}</Text>
            </View>
          )}

          {location.country && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Country</Text>
              <Text style={styles.infoValue}>{location.country}</Text>
            </View>
          )}

          {location.accuracy && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Accuracy</Text>
              <Text style={styles.infoValue}>{location.accuracy}m</Text>
            </View>
          )}
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="View on Map"
            onPress={() => {/* TODO: Open in map app */}}
            variant="primary"
          />
          
          <Button
            title="Get Directions"
            onPress={() => {/* TODO: Open directions */}}
            variant="secondary"
          />
          
          <Button
            title="Share Location"
            onPress={() => {/* TODO: Share location */}}
            variant="outline"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
