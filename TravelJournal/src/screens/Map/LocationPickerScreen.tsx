import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { RootStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = RootStackScreenProps<'LocationPicker'>;

export const LocationPickerScreen: React.FC<Props> = ({ route, navigation }) => {
  const theme = useTheme();
  const { onLocationSelect, initialLocation } = route.params;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  const handleSelectLocation = () => {
    // Mock location selection
    const mockLocation = {
      id: '1',
      latitude: 37.7749,
      longitude: -122.4194,
      placeName: 'San Francisco',
      city: 'San Francisco',
      country: 'USA',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    onLocationSelect(mockLocation);
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>📍 Select Location</Text>
        <Text style={styles.subtitle}>
          Location picker with map interface will be implemented here
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="Use Current Location"
            onPress={handleSelectLocation}
            variant="primary"
          />
          
          <Button
            title="Search Location"
            onPress={() => {/* TODO: Implement search */}}
            variant="secondary"
          />
          
          <Button
            title="Cancel"
            onPress={() => navigation.goBack()}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
