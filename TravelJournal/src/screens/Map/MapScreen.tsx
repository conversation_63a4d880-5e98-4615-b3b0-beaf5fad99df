import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { MapStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = MapStackScreenProps<'MapScreen'>;

export const MapScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  const mockLocation = {
    id: '1',
    latitude: 37.7749,
    longitude: -122.4194,
    placeName: 'San Francisco',
    city: 'San Francisco',
    country: 'USA',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>🗺️</Text>
        <Text style={styles.title}>Map View</Text>
        <Text style={styles.subtitle}>
          Interactive map with travel locations will be displayed here
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="View Sample Location"
            onPress={() => navigation.navigate('LocationDetail', { location: mockLocation })}
            variant="primary"
          />
          
          <Button
            title="View Route"
            onPress={() => navigation.navigate('RouteView', { mediaItems: [] })}
            variant="secondary"
          />
          
          <Button
            title="Current Location"
            onPress={() => {/* TODO: Get current location */}}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
