import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { CameraStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = CameraStackScreenProps<'GalleryPicker'>;

export const GalleryPickerScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>🖼️</Text>
        <Text style={styles.title}>Gallery</Text>
        <Text style={styles.subtitle}>
          Gallery picker will be implemented here
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="Select Photos"
            onPress={() => {/* TODO: Implement gallery picker */}}
            variant="primary"
          />
          
          <Button
            title="Select Videos"
            onPress={() => {/* TODO: Implement video picker */}}
            variant="secondary"
          />
          
          <Button
            title="Back to Camera"
            onPress={() => navigation.goBack()}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
