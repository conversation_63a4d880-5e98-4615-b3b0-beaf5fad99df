import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { CameraStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = CameraStackScreenProps<'CameraScreen'>;

export const CameraScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xxl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>📷</Text>
        <Text style={styles.title}>Camera</Text>
        <Text style={styles.subtitle}>
          Camera functionality will be implemented here
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="Take Photo"
            onPress={() => {/* TODO: Implement camera */}}
            variant="primary"
          />
          
          <Button
            title="Record Video"
            onPress={() => {/* TODO: Implement video recording */}}
            variant="secondary"
          />
          
          <Button
            title="Select from Gallery"
            onPress={() => navigation.navigate('GalleryPicker')}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
