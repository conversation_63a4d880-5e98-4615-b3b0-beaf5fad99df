import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { SettingsStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = SettingsStackScreenProps<'ExportSettings'>;

export const ExportSettingsScreen: React.FC<Props> = () => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      ...theme.shadows.small,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    description: {
      fontSize: theme.typography.fontSize.sm,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.md,
    },
    buttonContainer: {
      gap: theme.spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Export Format</Text>
          <Text style={styles.description}>
            Choose the default format for exporting your travel journals
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Image Export"
              onPress={() => {/* TODO: Set image format */}}
              variant="primary"
            />
            
            <Button
              title="Video Export"
              onPress={() => {/* TODO: Set video format */}}
              variant="outline"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quality Settings</Text>
          <Text style={styles.description}>
            Select the quality level for exported content
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              title="High Quality"
              onPress={() => {/* TODO: Set high quality */}}
              variant="primary"
            />
            
            <Button
              title="Medium Quality"
              onPress={() => {/* TODO: Set medium quality */}}
              variant="outline"
            />
            
            <Button
              title="Low Quality"
              onPress={() => {/* TODO: Set low quality */}}
              variant="outline"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Watermark</Text>
          <Text style={styles.description}>
            Add a watermark to your exported content
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Enable Watermark"
              onPress={() => {/* TODO: Toggle watermark */}}
              variant="outline"
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
