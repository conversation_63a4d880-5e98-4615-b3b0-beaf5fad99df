import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { SettingsStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = SettingsStackScreenProps<'APIConfiguration'>;

export const APIConfigurationScreen: React.FC<Props> = () => {
  const theme = useTheme();
  const [googleMapsKey, setGoogleMapsKey] = useState('');
  const [aiServiceKey, setAiServiceKey] = useState('');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      ...theme.shadows.small,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    description: {
      fontSize: theme.typography.fontSize.sm,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.md,
      lineHeight: theme.typography.lineHeight.sm,
    },
    label: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.medium,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    textInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.text,
      backgroundColor: theme.colors.background,
      marginBottom: theme.spacing.md,
    },
    buttonContainer: {
      gap: theme.spacing.sm,
    },
  });

  const handleSave = () => {
    // TODO: Save API keys securely
    console.log('Saving API keys:', { googleMapsKey, aiServiceKey });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Google Maps API</Text>
          <Text style={styles.description}>
            Enter your Google Maps API key to enable location services and map features.
          </Text>
          
          <Text style={styles.label}>API Key</Text>
          <TextInput
            style={styles.textInput}
            value={googleMapsKey}
            onChangeText={setGoogleMapsKey}
            placeholder="Enter Google Maps API key..."
            placeholderTextColor={theme.colors.textSecondary}
            secureTextEntry
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>AI Service API</Text>
          <Text style={styles.description}>
            Enter your AI service API key to enable automatic photo descriptions and content analysis.
          </Text>
          
          <Text style={styles.label}>API Key</Text>
          <TextInput
            style={styles.textInput}
            value={aiServiceKey}
            onChangeText={setAiServiceKey}
            placeholder="Enter AI service API key..."
            placeholderTextColor={theme.colors.textSecondary}
            secureTextEntry
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            title="Save Configuration"
            onPress={handleSave}
            variant="primary"
          />
          
          <Button
            title="Test Connection"
            onPress={() => {/* TODO: Test API connections */}}
            variant="outline"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
