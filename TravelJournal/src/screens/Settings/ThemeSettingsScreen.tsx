import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { SettingsStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme, useThemeContext } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = SettingsStackScreenProps<'ThemeSettings'>;

export const ThemeSettingsScreen: React.FC<Props> = () => {
  const theme = useTheme();
  const { isDark, toggleTheme, setThemeMode } = useThemeContext();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.lg,
      ...theme.shadows.small,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    currentTheme: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.md,
    },
    buttonContainer: {
      gap: theme.spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Theme</Text>
          <Text style={styles.currentTheme}>
            {isDark ? 'Dark Theme' : 'Light Theme'}
          </Text>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Toggle Theme"
              onPress={toggleTheme}
              variant="primary"
            />
            
            <Button
              title="Use System Theme"
              onPress={() => setThemeMode('system')}
              variant="outline"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Theme Options</Text>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Light Theme"
              onPress={() => setThemeMode('light')}
              variant={!isDark ? 'primary' : 'outline'}
            />
            
            <Button
              title="Dark Theme"
              onPress={() => setThemeMode('dark')}
              variant={isDark ? 'primary' : 'outline'}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
