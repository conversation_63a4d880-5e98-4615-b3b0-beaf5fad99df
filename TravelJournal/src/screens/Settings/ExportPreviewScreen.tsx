import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types
import { RootStackScreenProps } from '@/types/navigation';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Components
import { Button } from '@/components/common/Button/Button';

type Props = RootStackScreenProps<'ExportPreview'>;

export const ExportPreviewScreen: React.FC<Props> = ({ route, navigation }) => {
  const theme = useTheme();
  const { exportConfig } = route.params;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: theme.spacing.md,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontFamily: theme.typography.fontFamily.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.lg,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      fontFamily: theme.typography.fontFamily.regular,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xl,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: theme.spacing.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>📤 Export Preview</Text>
        <Text style={styles.subtitle}>
          Export preview will be displayed here
        </Text>
        
        <View style={styles.buttonContainer}>
          <Button
            title="Export"
            onPress={() => {/* TODO: Start export */}}
            variant="primary"
          />
          
          <Button
            title="Edit Settings"
            onPress={() => navigation.goBack()}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};
