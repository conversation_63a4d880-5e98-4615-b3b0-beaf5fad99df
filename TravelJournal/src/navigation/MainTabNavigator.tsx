import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';

// Types
import { MainTabParamList } from '@/types/navigation';

// Stack Navigators
import { TimelineStackNavigator } from './TimelineStackNavigator';
import { CameraStackNavigator } from './CameraStackNavigator';
import { MapStackNavigator } from './MapStackNavigator';
import { SettingsStackNavigator } from './SettingsStackNavigator';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

// Constants
import { DIMENSIONS } from '@/constants/Config';

const Tab = createBottomTabNavigator<MainTabParamList>();

export const MainTabNavigator: React.FC = () => {
  const theme = useTheme();

  const screenOptions = {
    headerShown: false,
    tabBarStyle: {
      backgroundColor: theme.colors.surface,
      borderTopColor: theme.colors.border,
      borderTopWidth: 1,
      height: DIMENSIONS.TAB_BAR_HEIGHT,
      paddingBottom: 8,
      paddingTop: 8,
    },
    tabBarActiveTintColor: theme.colors.primary,
    tabBarInactiveTintColor: theme.colors.textSecondary,
    tabBarLabelStyle: {
      fontFamily: theme.typography.fontFamily.medium,
      fontSize: theme.typography.fontSize.xs,
      marginTop: 4,
    },
    tabBarIconStyle: {
      marginBottom: 4,
    },
  };

  return (
    <Tab.Navigator screenOptions={screenOptions}>
      <Tab.Screen
        name="Timeline"
        component={TimelineStackNavigator}
        options={{
          title: 'Timeline',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'time' : 'time-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Camera"
        component={CameraStackNavigator}
        options={{
          title: 'Camera',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'camera' : 'camera-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Map"
        component={MapStackNavigator}
        options={{
          title: 'Map',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'map' : 'map-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Settings"
        component={SettingsStackNavigator}
        options={{
          title: 'Settings',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name={focused ? 'settings' : 'settings-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};
