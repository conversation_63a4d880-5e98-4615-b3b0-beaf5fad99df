import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';

// Types
import { RootStackParamList } from '@/types/navigation';

// Navigators
import { MainTabNavigator } from './MainTabNavigator';

// Screens
import { MediaDetailScreen } from '@/screens/Timeline/MediaDetailScreen';
import { EditMediaScreen } from '@/screens/Timeline/EditMediaScreen';
import { LocationPickerScreen } from '@/screens/Map/LocationPickerScreen';
import { CameraPreviewScreen } from '@/screens/Camera/CameraPreviewScreen';
import { ExportPreviewScreen } from '@/screens/Settings/ExportPreviewScreen';
import { OnboardingScreen } from '@/screens/Onboarding/OnboardingScreen';
import { PermissionsScreen } from '@/screens/Onboarding/PermissionsScreen';

// Selectors
import { selectIsFirstLaunch, selectHasRequiredPermissions } from '@/store/selectors/appSelectors';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

const Stack = createStackNavigator<RootStackParamList>();

export const AppNavigator: React.FC = () => {
  const theme = useTheme();
  const isFirstLaunch = useSelector(selectIsFirstLaunch);
  const hasRequiredPermissions = useSelector(selectHasRequiredPermissions);

  // Determine initial route based on app state
  const getInitialRouteName = (): keyof RootStackParamList => {
    if (isFirstLaunch) {
      return 'Onboarding';
    }
    if (!hasRequiredPermissions) {
      return 'Permissions';
    }
    return 'Main';
  };

  const screenOptions = {
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTintColor: theme.colors.text,
    headerTitleStyle: {
      fontFamily: theme.typography.fontFamily.medium,
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.text,
    },
    headerBackTitleVisible: false,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator
      initialRouteName={getInitialRouteName()}
      screenOptions={screenOptions}
    >
      {/* Onboarding Flow */}
      <Stack.Screen
        name="Onboarding"
        component={OnboardingScreen}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      
      <Stack.Screen
        name="Permissions"
        component={PermissionsScreen}
        options={{
          title: 'Permissions Required',
          headerLeft: () => null,
          gestureEnabled: false,
        }}
      />

      {/* Main App Flow */}
      <Stack.Screen
        name="Main"
        component={MainTabNavigator}
        options={{
          headerShown: false,
        }}
      />

      {/* Modal Screens */}
      <Stack.Group screenOptions={{ presentation: 'modal' }}>
        <Stack.Screen
          name="MediaDetail"
          component={MediaDetailScreen}
          options={({ route }) => ({
            title: route.params.mediaItem.type === 'photo' ? 'Photo Details' : 'Video Details',
            headerStyle: {
              ...screenOptions.headerStyle,
              backgroundColor: theme.colors.background,
            },
          })}
        />

        <Stack.Screen
          name="EditMedia"
          component={EditMediaScreen}
          options={{
            title: 'Edit Media',
            headerStyle: {
              ...screenOptions.headerStyle,
              backgroundColor: theme.colors.background,
            },
          }}
        />

        <Stack.Screen
          name="LocationPicker"
          component={LocationPickerScreen}
          options={{
            title: 'Select Location',
            headerStyle: {
              ...screenOptions.headerStyle,
              backgroundColor: theme.colors.background,
            },
          }}
        />

        <Stack.Screen
          name="CameraPreview"
          component={CameraPreviewScreen}
          options={{
            title: 'Preview',
            headerStyle: {
              ...screenOptions.headerStyle,
              backgroundColor: theme.colors.background,
            },
          }}
        />

        <Stack.Screen
          name="ExportPreview"
          component={ExportPreviewScreen}
          options={{
            title: 'Export Preview',
            headerStyle: {
              ...screenOptions.headerStyle,
              backgroundColor: theme.colors.background,
            },
          }}
        />
      </Stack.Group>
    </Stack.Navigator>
  );
};
