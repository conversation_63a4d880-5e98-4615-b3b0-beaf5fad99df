import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Types
import { MapStackParamList } from '@/types/navigation';

// Screens
import { MapScreen } from '@/screens/Map/MapScreen';
import { LocationDetailScreen } from '@/screens/Map/LocationDetailScreen';
import { RouteScreen } from '@/screens/Map/RouteScreen';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

const Stack = createStackNavigator<MapStackParamList>();

export const MapStackNavigator: React.FC = () => {
  const theme = useTheme();

  const screenOptions = {
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTintColor: theme.colors.text,
    headerTitleStyle: {
      fontFamily: theme.typography.fontFamily.medium,
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.text,
    },
    headerBackTitleVisible: false,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen
        name="MapScreen"
        component={MapScreen}
        options={{
          title: 'Map',
        }}
      />

      <Stack.Screen
        name="LocationDetail"
        component={LocationDetailScreen}
        options={({ route }) => ({
          title: route.params.location.placeName || 'Location Details',
        })}
      />

      <Stack.Screen
        name="RouteView"
        component={RouteScreen}
        options={{
          title: 'Route',
        }}
      />
    </Stack.Navigator>
  );
};
