import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Types
import { SettingsStackParamList } from '@/types/navigation';

// Screens
import { SettingsScreen } from '@/screens/Settings/SettingsScreen';
import { ThemeSettingsScreen } from '@/screens/Settings/ThemeSettingsScreen';
import { ExportSettingsScreen } from '@/screens/Settings/ExportSettingsScreen';
import { APIConfigurationScreen } from '@/screens/Settings/APIConfigurationScreen';
import { AboutScreen } from '@/screens/Settings/AboutScreen';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

const Stack = createStackNavigator<SettingsStackParamList>();

export const SettingsStackNavigator: React.FC = () => {
  const theme = useTheme();

  const screenOptions = {
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTintColor: theme.colors.text,
    headerTitleStyle: {
      fontFamily: theme.typography.fontFamily.medium,
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.text,
    },
    headerBackTitleVisible: false,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen
        name="SettingsScreen"
        component={SettingsScreen}
        options={{
          title: 'Settings',
        }}
      />

      <Stack.Screen
        name="ThemeSettings"
        component={ThemeSettingsScreen}
        options={{
          title: 'Theme Settings',
        }}
      />

      <Stack.Screen
        name="ExportSettings"
        component={ExportSettingsScreen}
        options={{
          title: 'Export Settings',
        }}
      />

      <Stack.Screen
        name="APIConfiguration"
        component={APIConfigurationScreen}
        options={{
          title: 'API Configuration',
        }}
      />

      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          title: 'About',
        }}
      />
    </Stack.Navigator>
  );
};
