import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Types
import { TimelineStackParamList } from '@/types/navigation';

// Screens
import { TimelineScreen } from '@/screens/Timeline/TimelineScreen';
import { MediaDetailScreen } from '@/screens/Timeline/MediaDetailScreen';
import { EditMediaScreen } from '@/screens/Timeline/EditMediaScreen';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

const Stack = createStackNavigator<TimelineStackParamList>();

export const TimelineStackNavigator: React.FC = () => {
  const theme = useTheme();

  const screenOptions = {
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTintColor: theme.colors.text,
    headerTitleStyle: {
      fontFamily: theme.typography.fontFamily.medium,
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.text,
    },
    headerBackTitleVisible: false,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen
        name="TimelineScreen"
        component={TimelineScreen}
        options={{
          title: 'Timeline',
        }}
      />

      <Stack.Screen
        name="MediaDetail"
        component={MediaDetailScreen}
        options={({ route }) => ({
          title: route.params.mediaItem.type === 'photo' ? 'Photo Details' : 'Video Details',
        })}
      />

      <Stack.Screen
        name="EditMedia"
        component={EditMediaScreen}
        options={{
          title: 'Edit Media',
        }}
      />
    </Stack.Navigator>
  );
};
