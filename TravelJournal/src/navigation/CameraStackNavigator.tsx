import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Types
import { CameraStackParamList } from '@/types/navigation';

// Screens
import { CameraScreen } from '@/screens/Camera/CameraScreen';
import { MediaPreviewScreen } from '@/screens/Camera/MediaPreviewScreen';
import { GalleryPickerScreen } from '@/screens/Camera/GalleryPickerScreen';

// Theme
import { useTheme } from '@/themes/ThemeProvider';

const Stack = createStackNavigator<CameraStackParamList>();

export const CameraStackNavigator: React.FC = () => {
  const theme = useTheme();

  const screenOptions = {
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTintColor: theme.colors.text,
    headerTitleStyle: {
      fontFamily: theme.typography.fontFamily.medium,
      fontSize: theme.typography.fontSize.lg,
      color: theme.colors.text,
    },
    headerBackTitleVisible: false,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator screenOptions={screenOptions}>
      <Stack.Screen
        name="CameraScreen"
        component={CameraScreen}
        options={{
          title: 'Camera',
          headerShown: false, // Camera screen typically has custom header
        }}
      />

      <Stack.Screen
        name="MediaPreview"
        component={MediaPreviewScreen}
        options={{
          title: 'Preview',
          headerShown: false, // Preview screen typically has custom header
        }}
      />

      <Stack.Screen
        name="GalleryPicker"
        component={GalleryPickerScreen}
        options={{
          title: 'Select from Gallery',
        }}
      />
    </Stack.Navigator>
  );
};
