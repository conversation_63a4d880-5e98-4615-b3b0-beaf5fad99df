import { createNavigationContainerRef, StackActions } from '@react-navigation/native';
import { RootStackParamList } from '@/types/navigation';

// Navigation reference for imperative navigation
export const navigationRef = createNavigationContainerRef<RootStackParamList>();

// Navigation helper functions
export const navigate = (name: keyof RootStackParamList, params?: any) => {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name as never, params as never);
  }
};

export const goBack = () => {
  if (navigationRef.isReady() && navigationRef.canGoBack()) {
    navigationRef.goBack();
  }
};

export const reset = (routeName: keyof RootStackParamList, params?: any) => {
  if (navigationRef.isReady()) {
    navigationRef.reset({
      index: 0,
      routes: [{ name: routeName as never, params: params as never }],
    });
  }
};

export const push = (name: keyof RootStackParamList, params?: any) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.push(name as never, params as never));
  }
};

export const replace = (name: keyof RootStackParamList, params?: any) => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.replace(name as never, params as never));
  }
};

export const getCurrentRoute = () => {
  if (navigationRef.isReady()) {
    return navigationRef.getCurrentRoute();
  }
  return null;
};

export const getCurrentRouteName = () => {
  const route = getCurrentRoute();
  return route?.name;
};

export const getRouteParams = () => {
  const route = getCurrentRoute();
  return route?.params;
};
