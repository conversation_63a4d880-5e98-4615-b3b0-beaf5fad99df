{"name": "travel-journal", "version": "1.0.0", "description": "A comprehensive mobile travel journal application", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "@react-navigation/bottom-tabs": "^6.5.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.1.0", "expo": "~49.0.0", "expo-camera": "~13.4.0", "expo-image-picker": "~14.3.0", "expo-location": "~16.1.0", "expo-sqlite": "~11.3.0", "expo-secure-store": "~12.3.0", "expo-av": "~13.4.0", "expo-media-library": "~15.4.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.0", "react-native-maps": "1.7.1", "react-native-paper": "^5.10.0", "react-native-vector-icons": "^10.0.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-image-resizer": "^3.0.0", "react-native-google-places-autocomplete": "^2.5.0", "expo-image-manipulator": "~11.3.0", "expo-sharing": "~11.5.0", "expo-file-system": "~15.4.0", "@react-native-async-storage/async-storage": "1.18.2", "react-native-svg": "13.9.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.2.1", "prettier": "^3.0.0", "typescript": "^5.1.3", "@testing-library/react-native": "^12.0.0", "@testing-library/jest-native": "^5.4.0"}, "keywords": ["expo", "react-native", "typescript", "travel", "journal", "mobile"], "author": "Travel Journal Team", "license": "MIT", "private": true}