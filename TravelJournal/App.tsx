import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { StyleSheet } from 'react-native';

// Navigation
import { NavigationContainer } from '@react-navigation/native';
import { AppNavigator } from '@/navigation/AppNavigator';

// Store
import { store, persistor } from '@/store';

// Theme
import { ThemeProvider } from '@/themes/ThemeProvider';

// Components
import { LoadingScreen } from '@/components/common/Loading/LoadingScreen';
import { ErrorBoundary } from '@/components/common/ErrorBoundary/ErrorBoundary';

// Utils
import { navigationRef } from '@/navigation/navigationUtils';

export default function App() {
  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={styles.container}>
        <SafeAreaProvider>
          <Provider store={store}>
            <PersistGate loading={<LoadingScreen />} persistor={persistor}>
              <ThemeProvider>
                <NavigationContainer ref={navigationRef}>
                  <StatusBar style="auto" />
                  <AppNavigator />
                </NavigationContainer>
              </ThemeProvider>
            </PersistGate>
          </Provider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
