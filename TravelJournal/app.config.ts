import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: 'Travel Journal',
  slug: 'travel-journal',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'automatic',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#ffffff'
  },
  assetBundlePatterns: [
    '**/*'
  ],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.traveljournal.app',
    infoPlist: {
      NSCameraUsageDescription: 'This app uses the camera to capture photos and videos for your travel journal.',
      NSPhotoLibraryUsageDescription: 'This app accesses your photo library to select images for your travel journal.',
      NSLocationWhenInUseUsageDescription: 'This app uses location services to tag your photos with location information.',
      NSLocationAlwaysAndWhenInUseUsageDescription: 'This app uses location services to tag your photos with location information.',
      NSMicrophoneUsageDescription: 'This app uses the microphone to record audio for videos in your travel journal.'
    }
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#FFFFFF'
    },
    package: 'com.traveljournal.app',
    permissions: [
      'CAMERA',
      'RECORD_AUDIO',
      'READ_EXTERNAL_STORAGE',
      'WRITE_EXTERNAL_STORAGE',
      'ACCESS_FINE_LOCATION',
      'ACCESS_COARSE_LOCATION',
      'ACCESS_MEDIA_LOCATION'
    ]
  },
  web: {
    favicon: './assets/favicon.png'
  },
  plugins: [
    'expo-camera',
    'expo-image-picker',
    'expo-location',
    'expo-media-library',
    [
      'expo-sqlite',
      {
        enableFTS: true
      }
    ],
    [
      'react-native-maps',
      {
        googleMapsApiKey: process.env.GOOGLE_MAPS_API_KEY
      }
    ]
  ],
  extra: {
    eas: {
      projectId: 'your-project-id-here'
    }
  }
});
